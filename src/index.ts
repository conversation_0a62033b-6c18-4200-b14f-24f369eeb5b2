#!/usr/bin/env node

import * as k8s from '@kubernetes/client-node';
import { Pool } from 'pg';
import type { PgResource, PgResourceSpec, PgStatus } from './resource-type.js';

class PostgresController {
  private readonly kc: k8s.KubeConfig;
  private readonly k8sApi: k8s.CoreV1Api;
  private readonly customObjectsApi: k8s.CustomObjectsApi;

  private readonly group = 'db.seabury.app';
  private readonly version = 'v1';
  private readonly plural = 'postgresresources';

  constructor() {
    // Load Kubernetes config
    this.kc = new k8s.KubeConfig();
    if (process.env['KUBERNETES_SERVICE_HOST']) {
      this.kc.loadFromCluster();
    } else {
      this.kc.loadFromDefault();
    }

    this.k8sApi = this.kc.makeApiClient(k8s.CoreV1Api);
    this.customObjectsApi = this.kc.makeApiClient(k8s.CustomObjectsApi);

    console.log(`${PostgresController.name} initialized`);
  }

  generatePassword(length = 32) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_()*&^%$#!~';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  async getAdminCredentials(secretRef: PgResourceSpec['postgresInstance']['adminSecretRef'], namespace: string) {

    try {
      const response = await this.k8sApi.readNamespacedSecret({
        name: secretRef.name,
        namespace: secretRef.namespace || namespace
      });
      const secret = response.data;
      if (!secret || !secret['username'] || !secret['password']) {
        throw new Error('Invalid secret, missing required username or password');
      }

      const username = Buffer.from(secret['username'], 'base64').toString('utf-8');
      const password = Buffer.from(secret['password'], 'base64').toString('utf-8');

      return { username, password };
    } catch (error: any) {
      console.error('Failed to get admin credentials:', error.message);
      throw error;
    }
  }

  async connectPostgres(host: string, port: number, username: string, password: string, database = 'postgres') {
    const client = new Pool({
      host,
      port,
      user: username,
      password,
      database,
    });

    try {
      await client.connect();
      console.log(`Connected to PostgreSQL at ${host}:${port}`);
      return client;
    } catch (error: any) {
      console.error('Failed to connect to PostgreSQL:', error.message);
      throw error;
    }
  }

  async databaseExists(client: Pool, databaseName: string) {
    try {
      const result = await client.query(
        'SELECT 1 FROM pg_database WHERE datname = $1',
        [databaseName]
      );
      return result.rows.length > 0;
    } catch (error: any) {
      console.error('Error checking if database exists:', error.message);
      throw error;
    }
  }

  async userExists(client: Pool, username: string) {
    try {
      const result = await client.query(
        'SELECT 1 FROM pg_user WHERE usename = $1',
        [username]
      );
      return result.rows.length > 0;
    } catch (error: any) {
      console.error('Error checking if user exists:', error.message);
      throw error;
    }
  }

  async createDatabase(client: Pool, databaseName: string) {
    try {
      // Note: Cannot use parameterized queries for DDL statements
      await client.query(`CREATE DATABASE "${databaseName}"`);
      console.log(`Created database: ${databaseName}`);
    } catch (error: any) {
      console.error('Error creating database:', error.message);
      throw error;
    }
  }

  async createUser(client: Pool, username: string, password: string) {
    try {
      await client.query(`CREATE USER "${username}" WITH PASSWORD $1`, [password]);
      console.log(`Created user: ${username}`);
    } catch (error: any) {
      console.error('Error creating user:', error.message);
      throw error;
    }
  }

  async grantPrivileges(client: Pool, databaseName: string, username: string, host: string, port: number, adminCreds: { username: string; password: string; }) {
    let dbClient: Pool | undefined = undefined

    try {
      // Grant database privileges
      await client.query(`GRANT ALL PRIVILEGES ON DATABASE "${databaseName}" TO "${username}"`);

      // Connect to the specific database for schema privileges
      await client.end();

      dbClient = await this.connectPostgres(host, port, adminCreds.username, adminCreds.password, databaseName);

      await dbClient.query(`GRANT ALL ON SCHEMA public TO "${username}"`);
      await dbClient.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO "${username}"`);
      await dbClient.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO "${username}"`);

      await dbClient.end();
      console.log(`Granted privileges to user: ${username}`);
    } catch (error: any) {
      console.error('Error granting privileges:', error.message);
      throw error;
    } finally {
      if (dbClient) {
        await dbClient.end();
      }
    }
  }

  async createSecret(name: string, namespace: string, data: Record<string, string>) {
    const secretData = Object.entries(data).reduce<Record<string, string>>((acc, [k, v]) => {
      acc[k] = Buffer.from(v).toString('base64');
      return acc;
    }, { });

    const secret = {
      apiVersion: 'v1',
      kind: 'Secret',
      metadata: {
        name,
        namespace,
        labels: {
          'managed-by': 'postgres-controller',
          'app.kubernetes.io/created-by': 'postgres-controller',
        },
      },
      data: secretData,
    };

    try {
      await this.k8sApi.createNamespacedSecret({ namespace, body: secret });
      console.log(`Created secret ${name} in namespace ${namespace}`);
    } catch (error: any) {
      if (error.response && error.response.statusCode === 409) {
        // Secret already exists, update it
        await this.k8sApi.patchNamespacedSecret({ name, namespace, body: secret });
        console.log(`Updated secret ${name} in namespace ${namespace}`);
      } else {
        throw error;
      }
    }
  }

  async updateStatus(name: string, namespace: string, status: Pick<PgStatus, 'phase' | 'message'> & Partial<Omit<PgStatus, 'phase' | 'message' | 'lastUpdated'>>) {

    try {
      await this.customObjectsApi.patchNamespacedCustomObject({
        group: this.group,
        version: this.version,
        namespace,
        name,
        plural: this.plural,
        body: {
          status: {
            ...status,
            lastUpdated: new Date()
          }
        },
      });
    } catch (error: any) {
      console.error('Failed to update status:', error.message);
    }
  }

  async reconcileResource(resource: PgResource) {
    const metadata = resource.metadata || {};
    const spec = resource.spec || {};

    const name = metadata.name;
    const namespace = metadata.namespace;

    console.log(`Reconciling PostgresResource ${namespace}/${name}`);
    let pool: Pool | undefined = undefined;

    try {
      // Update status to Creating
      await this.updateStatus(name, namespace, {
        phase: 'Creating',
        message: 'Starting resource creation',
      });

      // Get connection details
      const pgInstance = spec.postgresInstance;
      const host = pgInstance.host;
      const port = pgInstance.port || 5432;
      const adminSecretRef = pgInstance.adminSecretRef;

      const adminCreds = await this.getAdminCredentials(adminSecretRef, namespace);
      pool = await this.connectPostgres(host, port, adminCreds.username, adminCreds.password, pgInstance.databaseName);

      const databaseName = spec.databaseName;
      const username = spec.username;
      const password = this.generatePassword();

      // Create database if not exists
      if (!(await this.databaseExists(pool, databaseName))) {
        await this.createDatabase(pool, databaseName);
        await this.updateStatus(name, namespace, {
          phase: 'Creating',
          message: 'Database created',
          databaseCreated: true
        });
      }

      // Create user if not exists
      if (!(await this.userExists(pool, username))) {
        await this.createUser(pool, username, password);
        await this.updateStatus(name, namespace, {
          phase: 'Creating',
          message: 'User created',
          userCreated: true
        });
      }

      // Grant privileges
      // TODO: Support customizing privileges
      // const privileges = spec.privileges || ['ALL'];
      await this.grantPrivileges(pool, databaseName, username, host, port, adminCreds);

      // Create secret
      const secretName = spec.secretName || `${name}-credentials`;
      const secretData = {
        host: host,
        port: port.toString(),
        database: databaseName,
        username: username,
        password: password,
        url: `postgresql://${username}:${password}@${host}:${port}/${databaseName}`,
      };

      await this.createSecret(secretName, namespace, secretData);

      // Update status to Ready
      await this.updateStatus(name, namespace, {
        phase: 'Ready',
        message: 'PostgreSQL resources created successfully',
        databaseCreated: true,
        userCreated: true,
        secretCreated: true,
      });

      console.log(`Successfully reconciled PostgresResource ${namespace}/${name}`);
    } catch (error: any) {
      console.error(`Failed to reconcile PostgresResource ${namespace}/${name}:`, error.message);
      await this.updateStatus(name, namespace, {
        phase: 'Failed',
        message: error.message,
      });
    } finally {
      if (pool) {
        await pool.end();
      }
    }
  }

  async handleDeletion(resource: PgResource) {
    const metadata = resource.metadata || {};
    const spec = resource.spec || {};

    console.log(`Handling deletion for ${metadata.namespace}/${metadata.name}`);

    let client: Pool | undefined = undefined;

    try {
      const { name, namespace } = metadata;
      const pgInstance = spec.postgresInstance;
      const host = pgInstance.host || 'postgres-service';
      const port = pgInstance.port || 5432;
      const adminSecretRef = pgInstance.adminSecretRef;

      if (spec.dropOnDelete) {

        const adminCreds = await this.getAdminCredentials(adminSecretRef, metadata.namespace);
        client = await this.connectPostgres(host, port, adminCreds.username, adminCreds.password);
        // Drop database and user
        await client.query(`DROP DATABASE IF EXISTS "${spec.databaseName}"`);
        await client.query(`DROP USER IF EXISTS "${spec.username}"`);
        await client.end();

        await this.updateStatus(name, namespace, {
          phase: 'Deleting',
          message: 'Database dropped'
        });
      } else {
        console.log('Skipping drop database');
      }

      // Delete secret
      const secretName = spec.secretName || `${metadata.name}-credentials`;
      try {
        await this.k8sApi.deleteNamespacedSecret({ name: secretName, namespace });
        await this.updateStatus(name, namespace, {
          phase: 'Deleting',
          message: 'Secret removed'
        });
      } catch (error: any) {
        // Ignore if secret doesn't exist
        if (error.response && error.response.statusCode !== 404) {
          throw error;
        }
      }

      console.log(`Cleanup completed for ${metadata.namespace}/${metadata.name}`);
    } catch (error: any) {
      console.error(`Failed to cleanup ${metadata.namespace}/${metadata.name}:`, error.message);
    } finally {
      if (client) {
        await client.end();
      }
    }
  }

  async watchResources() {
    console.log('Starting to watch PostgresResources...');

    const watch = new k8s.Watch(this.kc);

    const watchOptions = {
      allowWatchBookmarks: true,
    };

    const startWatch = async () => {
      await watch.watch(
        `/apis/${this.group}/${this.version}/${this.plural}`,
        watchOptions,
        async (type, obj) => {
          try {
            console.log(`Received ${type} event for ${obj.metadata.namespace}/${obj.metadata.name}`);

            if (type === 'ADDED' || type === 'MODIFIED') {
              await this.reconcileResource(obj);
            } else if (type === 'DELETED') {
              await this.handleDeletion(obj);
            }
          } catch (error: any) {
            console.error('Error processing watch event:', error.message);
          }
        },
        (error: any) => {
          console.error('Watch error:', error.message);
          console.log('Restarting watch in 5 seconds...');
          setTimeout(startWatch, 5000);
        }
      );
    };

    await startWatch();
  }
}

async function main() {
  const controller = new PostgresController();
  await controller.watchResources();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

main().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
